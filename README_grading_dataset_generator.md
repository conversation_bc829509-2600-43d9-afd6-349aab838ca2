# Submission Grading Dataset Generator

This tool creates presentable markdown files from pilot testing Excel data to help teachers verify AI grading methodology by allowing them to grade each submission and marking point independently.

## Overview

The `submission_grading_dataset_generator.py` script processes the pilot testing report Excel file and generates structured markdown documents that teachers can use to:

1. **Review student submissions** organized by question and part
2. **Grade each marking point independently** with scores of 0, 0.5, or 1
3. **Provide clean evaluation** without AI bias (no AI scores shown)
4. **Filter by topic** to focus on specific subject areas
5. **Document feedback and comments** for each submission

## Features

- **Topic Filtering**: Filter submissions by specific topics (e.g., "alkenes", "alcohols", "atomic-structure")
- **Organized Structure**: Submissions grouped by question and part for easy navigation
- **Marking Point Analysis**: Each marking point displayed separately for independent evaluation
- **Semantic Clustering**: Groups similar answers together when available
- **Teacher Scoring Forms**: Built-in checkboxes and comment fields for teacher evaluation
- **Summary Statistics**: Overview of questions, submissions, and data source

## Usage

### Basic Usage

```bash
# Generate dataset for all submissions
python3 submission_grading_dataset_generator.py

# Generate dataset for specific topic
python3 submission_grading_dataset_generator.py --topic alkenes

# Specify custom output filename
python3 submission_grading_dataset_generator.py --topic alcohols --output alcohols_review.md
```

### Command Line Options

- `--excel-file`: Path to pilot testing Excel file (default: `pilot_testing_report_2025-07-24_to_2025-07-25.xlsx`)
- `--topic`: Filter by topic (case-insensitive partial match)
- `--output`: Custom output filename (auto-generated if not specified)

### Available Topics

Based on the pilot testing data, the following topics are available:

- **alcohols** (207 submissions)
- **alkanes** (114 submissions) 
- **alkenes** (75 submissions)
- **arenes** (55 submissions)
- **atomic-structure** (87 submissions)
- **carbonyl-compounds** (31 submissions)
- **carboxylic-acids** (22 submissions)
- **chemical-bonding-1** (94 submissions)
- **energetics** (7 submissions)
- **equilibria** (21 submissions)
- **gaseous-state** (19 submissions)
- **nitrogen-compounds** (25 submissions)
- **organic-chemistry** (9 submissions)
- **periodic-table** (20 submissions)
- **periodic-table-2** (6 submissions)
- **redox** (6 submissions)
- **solubility** (6 submissions)
- **transition-elements** (3 submissions)

## Generated Files

The following sample files have been generated:

1. **`clean_alkenes_grading_dataset.md`** - 75 submissions focused on alkenes topic (clean version)
2. **`clean_periodic_table_2_grading.md`** - 6 submissions for periodic table concepts (clean version)
3. **`complete_grading_dataset.md`** - All 807 submissions (large file)

**Note**: The "clean" versions contain only student answers and marking points without any AI grading information, providing unbiased teacher evaluation.

## Markdown Structure

Each generated markdown file contains:

### Header Section
- Title with topic filter (if applied)
- Generation timestamp
- Instructions for teachers
- Scoring guide (0, 0.5, 1.0)

### Question Sections
- Organized by question and part
- Submission count per section
- Semantic clustering when available

### Individual Submissions
For each submission:
- Student identifier
- Student answer text
- Maximum possible score
- Marking points breakdown with:
  - Point description
  - Teacher scoring checkboxes (0, 0.5, 1)
  - Teacher comment fields
- Overall teacher score field
- General teacher comments

### Summary Section
- Total questions and submissions
- Data source information
- Notes for teachers

## Teacher Instructions

When using the generated markdown files:

1. **Print or use digital annotation** tools to mark the documents
2. **Focus on consistency** across similar answers
3. **Evaluate each marking point independently** without bias
4. **Pay attention to partial credit** scenarios (0.5 scores)
5. **Document any marking criteria** that need clarification
6. **Provide detailed comments** to justify scoring decisions

## Benefits for Grading Methodology Verification

- **Unbiased Assessment**: Teachers grade without seeing any AI results
- **Marking Point Granularity**: Each criterion evaluated separately
- **Pattern Recognition**: Similar answers grouped together
- **Systematic Review**: Organized structure ensures comprehensive coverage
- **Documentation**: Built-in fields for feedback and comments
- **Scalability**: Can focus on specific topics or review entire dataset

## File Sizes and Recommendations

- **Complete dataset**: ~738KB (all 807 submissions) - recommended for comprehensive review
- **Topic-specific**: 7-64KB depending on topic - recommended for focused analysis
- **Small topics** (< 10 submissions): Good for initial testing and training
- **Large topics** (> 100 submissions): Better for statistical significance

## Technical Notes

- Requires Python 3 with pandas and openpyxl libraries
- Processes Excel file from pilot testing data extractor
- Handles missing or malformed marking point data gracefully
- Generates UTF-8 encoded markdown files
- Auto-generates timestamps and filenames

## Next Steps

1. **Select appropriate topic** based on your focus area
2. **Generate markdown file** using the script
3. **Review and grade submissions** independently
4. **Compare with AI grading** to identify patterns
5. **Document findings** for methodology improvement
6. **Iterate on marking criteria** based on teacher feedback

This tool provides a systematic approach to validating AI grading methodology through human expert review, ensuring accuracy and consistency in automated assessment systems.
