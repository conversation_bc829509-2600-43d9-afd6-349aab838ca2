#!/usr/bin/env python3
"""
List Available Topics
Simple script to show all available topics in the pilot testing data with submission counts.
"""

import pandas as pd
import sys

def main():
    excel_file = 'pilot_testing_report_2025-07-24_to_2025-07-25.xlsx'
    
    try:
        # Load the detailed submissions sheet
        df = pd.read_excel(excel_file, sheet_name='Detailed Submissions')
        print(f"Loaded {len(df)} submissions from {excel_file}")
        print("\nAvailable topics with submission counts:")
        print("=" * 50)
        
        # Get topic counts
        topic_counts = df['Topic'].value_counts().sort_index()
        
        total_submissions = 0
        for topic, count in topic_counts.items():
            print(f"  {topic:<25} : {count:>3} submissions")
            total_submissions += count
        
        print("=" * 50)
        print(f"  {'TOTAL':<25} : {total_submissions:>3} submissions")
        print(f"  {'UNIQUE TOPICS':<25} : {len(topic_counts):>3} topics")
        
        print("\nUsage examples:")
        print("  python3 submission_grading_dataset_generator.py --topic alkenes")
        print("  python3 submission_grading_dataset_generator.py --topic 'atomic-structure'")
        print("  python3 submission_grading_dataset_generator.py --topic alcohols --output alcohols_review.md")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
