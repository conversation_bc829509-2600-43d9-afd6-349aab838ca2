#!/usr/bin/env python3
"""
Submission Grading Dataset Generator
Creates a presentable markdown file from pilot testing Excel data for teacher grading verification.
Allows filtering by topic and provides detailed marking point analysis.
"""

import pandas as pd
import openpyxl
import json
import argparse
import sys
from datetime import datetime
from collections import defaultdict
import re

def load_excel_data(excel_file):
    """Load data from the pilot testing Excel file"""
    try:
        # Load the detailed submissions sheet
        df = pd.read_excel(excel_file, sheet_name='Detailed Submissions')
        print(f"Loaded {len(df)} submissions from Excel file")
        return df
    except Exception as e:
        print(f"Error loading Excel file: {e}")
        sys.exit(1)

def parse_marking_point_feedback(feedback_text):
    """Parse the marking point evaluation feedback into structured data"""
    if pd.isna(feedback_text) or not feedback_text:
        return []
    
    marking_points = []
    # Split by semicolon and parse each marking point
    points = feedback_text.split(';')
    for point in points:
        if ':' in point:
            description, evaluation = point.split(':', 1)
            marking_points.append({
                'description': description.strip(),
                'evaluation': evaluation.strip()
            })
    return marking_points

def filter_by_topic(df, topic_filter=None):
    """Filter submissions by topic if specified"""
    if topic_filter:
        original_count = len(df)
        df = df[df['Topic'].str.contains(topic_filter, case=False, na=False)]
        print(f"Filtered by topic '{topic_filter}': {len(df)} submissions (from {original_count})")
    return df

def group_submissions_by_question_part(df):
    """Group submissions by question and part for organized presentation"""
    grouped = defaultdict(lambda: defaultdict(list))
    
    for _, row in df.iterrows():
        question_key = f"{row['Topic']} - {row['Question Description']}"
        part_key = f"Part {row['Part ID']}: {row['Part Description']}"
        
        # Parse marking points
        marking_points = parse_marking_point_feedback(row['Marking Point Evaluation Feedback'])
        
        submission = {
            'username': row['Student Answer'],
            'answer': row['Student Answer Text'],
            'score': row['Score'],
            'max_score': row['Max Score'],
            'marking_points': marking_points,
            'timestamp': row['Timestamp'],
            'semantic_cluster': row.get('Semantic Cluster', 'N/A')
        }
        
        grouped[question_key][part_key].append(submission)
    
    return grouped

def generate_markdown_content(grouped_data, topic_filter=None):
    """Generate markdown content for teacher grading"""
    
    # Header
    title = f"Submission Grading Dataset for Teacher Verification"
    if topic_filter:
        title += f" - Topic: {topic_filter}"
    
    markdown = f"""# {title}

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Instructions for Teachers

This document contains student submissions organized by question and part. For each submission, you can:

1. **Review the student's answer**
2. **Evaluate each marking point independently** 
3. **Assign scores** (0, 0.5, or 1) for each marking point
4. **Compare with AI grading** to verify methodology

### Scoring Guide
- **1.0**: Fully correct/complete
- **0.5**: Partially correct/incomplete  
- **0.0**: Incorrect/missing

---

"""

    question_count = 0
    total_submissions = 0
    
    for question_key, parts in grouped_data.items():
        question_count += 1
        markdown += f"## Question {question_count}: {question_key}\n\n"
        
        for part_key, submissions in parts.items():
            markdown += f"### {part_key}\n\n"
            markdown += f"**Total Submissions:** {len(submissions)}\n\n"
            
            # Group by semantic cluster if available
            clusters = defaultdict(list)
            for sub in submissions:
                cluster = sub.get('semantic_cluster', 'N/A')
                clusters[cluster].append(sub)
            
            submission_count = 0
            for cluster, cluster_submissions in clusters.items():
                if cluster != 'N/A':
                    markdown += f"#### Semantic Cluster: {cluster}\n\n"
                
                for submission in cluster_submissions:
                    submission_count += 1
                    total_submissions += 1
                    
                    markdown += f"**Submission {submission_count}** (Student: {submission['username']})\n\n"
                    markdown += f"**Answer:** {submission['answer']}\n\n"
                    markdown += f"**AI Score:** {submission['score']}/{submission['max_score']}\n\n"
                    
                    if submission['marking_points']:
                        markdown += "**Marking Points Evaluation:**\n\n"
                        for i, mp in enumerate(submission['marking_points'], 1):
                            markdown += f"{i}. **{mp['description']}**\n"
                            markdown += f"   - AI Evaluation: {mp['evaluation']}\n"
                            markdown += f"   - Teacher Score: [ ] 0 [ ] 0.5 [ ] 1\n"
                            markdown += f"   - Teacher Comments: ________________\n\n"
                    else:
                        markdown += "**No marking points available**\n\n"
                    
                    markdown += f"**Teacher Overall Score:** ___/{submission['max_score']}\n\n"
                    markdown += f"**Teacher Comments:** ________________\n\n"
                    markdown += "---\n\n"
            
            markdown += "\n"
        
        markdown += "\n"
    
    # Summary
    markdown += f"""## Summary

- **Total Questions:** {question_count}
- **Total Submissions:** {total_submissions}
- **Generated from:** pilot_testing_report_2025-07-24_to_2025-07-25.xlsx

## Notes for Teachers

1. Focus on marking point consistency across similar answers
2. Note any patterns in AI grading that seem incorrect
3. Pay attention to partial credit scenarios
4. Document any marking criteria that need clarification

"""
    
    return markdown

def main():
    parser = argparse.ArgumentParser(description='Generate markdown grading dataset from pilot testing Excel file')
    parser.add_argument('--excel-file', default='pilot_testing_report_2025-07-24_to_2025-07-25.xlsx',
                       help='Path to the pilot testing Excel file')
    parser.add_argument('--topic', help='Filter submissions by topic (case-insensitive partial match)')
    parser.add_argument('--output', help='Output markdown file name (auto-generated if not specified)')
    
    args = parser.parse_args()
    
    # Load data
    print("Loading Excel data...")
    df = load_excel_data(args.excel_file)
    
    # Filter by topic if specified
    if args.topic:
        df = filter_by_topic(df, args.topic)
        if len(df) == 0:
            print(f"No submissions found for topic '{args.topic}'")
            sys.exit(1)
    
    # Group submissions
    print("Grouping submissions by question and part...")
    grouped_data = group_submissions_by_question_part(df)
    
    # Generate markdown
    print("Generating markdown content...")
    markdown_content = generate_markdown_content(grouped_data, args.topic)
    
    # Determine output filename
    if args.output:
        output_file = args.output
    else:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        topic_suffix = f"_{args.topic.replace(' ', '_')}" if args.topic else ""
        output_file = f"teacher_grading_dataset{topic_suffix}_{timestamp}.md"
    
    # Write to file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        print(f"Successfully generated: {output_file}")
        print(f"File size: {len(markdown_content)} characters")
    except Exception as e:
        print(f"Error writing output file: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
